<template>
    <div class="detail-box shadow top-box">
        <div class="timeline-top-simple" v-show="showSimple">
            <Select
                :isOpen.sync="isDropdownOpen"
                class="timeline-top-select"
                @datachange="typeSelectClick"
                :noBorder="true"
                :dataSource="renderLabels"
                :value="currentType"
                dropdownCls="timeline-top-dropdown-wrap"
            ></Select>
            <div class="timeline-top-notetext">
                <input
                    class="chime-input large"
                    type="text"
                    :placeHolder="$t('activities.addNote')"
                    @click="changeMode(false)"
                />
            </div>
        </div>
        <div class="timeline-top-complex" v-show="!showSimple">
            <ul>
                <li
                    v-for="(item, index) in renderLabels"
                    :key="index + 'timelineTop'"
                    :class="currentType === item.id ? 'active' : ''"
                    @click="changeType(item.id)"
                >
                    {{ item.name }}
                </li>
            </ul>
            <div class="content" :class="currentType">
                <ul class="add-timeline-call clearfix" v-show="callWrapShow">
                    <li class="call-number call-option">
                        <span class="list-label">{{ $t("activities.to") }}</span>
                        <div class="call-number-list" style="width: 150px">
                            <Select
                                v-if="phoneArr.length > 1"
                                :value="callNormPhone.number"
                                valueMember="phone"
                                size="small"
                                :dataSource="phoneArr"
                                @datachange="onCallPhoneChange"
                                dropdownCls="detail-box dropdown-wrap bottom btn-drop-wrap detail-info-dropdown"
                            >
                                <template #label>
                                    <span>
                                        {{ callNormPhone.text }}
                                    </span>
                                </template>
                                <template #category="{ option }">
                                    <li class="btn-drop-item desc-item" v-if="option.isCategory">
                                        <div class="text">
                                            {{ option.fullName }}
                                        </div>
                                        <div class="desc" v-if="option.relation">
                                            {{ option.relation }}
                                        </div>
                                    </li>
                                </template>
                                <template #default="{ option }">
                                    <li class="btn-drop-item click-item">
                                        <i class="icon2017" :class="findIconCls(option)"></i>
                                        <div class="text">
                                            {{ option.normPhone.text }}
                                        </div>
                                    </li>
                                </template>
                            </Select>
                            <span v-else class="phone-span">
                                {{ phoneArr.length ? phoneArr[0].normPhone.text : notAppl }}
                            </span>
                        </div>
                    </li>
                    <li class="call-outcome call-option">
                        <span class="list-label">{{ $t("activities.outcome") }}</span>
                        <div class="call-outcome-list" style="display: inline-block; width: 150px">
                            <Select
                                class="outcome-select-wrap"
                                v-bind="callTypeList"
                                @datachange="
                                    ({ value }) => {
                                        callTypeList.value = value;
                                    }
                                "
                            ></Select>
                        </div>
                    </li>
                </ul>
                <timeline-input
                    :showSimple="showSimple"
                    :autofocus="true"
                    :currentType="currentType"
                    :sendContent="sendContent"
                    v-bind="inputConfig"
                    @getContent="addTimeline"
                    ref="timelineInput"
                ></timeline-input>
                <div class="bottom-btns">
                    <CheckBox
                        class="pin-top"
                        :label="$t('activities.pinToPTop')"
                        :checked="popChecked"
                        @change="pinToTop($event.checked)"
                    ></CheckBox>
                    <div class="complex-btns">
                        <span class="chime-btn invisible" @click="changeMode(true)">{{
                            $st("common", "popWin.cancelText")
                        }}</span>
                        <span
                            class="chime-btn primary"
                            @click="getContent"
                            :class="{ disabled: addBTnDisabled }"
                            >{{ addBtnText }}</span
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { crmUtils, official, localStore, track, infoData, phoneHelper } from "crm";
import { components, utils, popMgr } from "common";
import { mapState, mapActions } from "vuex";
import TimelineInput from "./TimelineInput.vue";
import http from "../../api";
const { getNormPhone, setAttrFromNormPhone } = phoneHelper;

//  Pay attention to distinguish modules/dialer-common/CallOutcome.vue  constants in ， here value no spaces inside
const UPDATE_AI_OUTCOME_ARR = ["Talked", "BadNumber", "DNCNumber", "DNCContact"];
const { Select, CheckBox } = components;
const componentName = "TimelineTop";

function getPhoneList(contactInfo) {
    let arr = [];
    console.log(contactInfo, "contactInfo");
    contactInfo.forEach((member) => {
        let _pl = member?.userPhoneList ?? [];
        _pl = _pl.map((v) => {
            return {
                ...v,
                fullName: member.fullName,
                relation: member.relation,
                dnc: member.dnc
            };
        });
        _pl.length && (arr = arr.concat(_pl));
    });
    console.log(arr, "arr");
    return arr;
}
export default {
    name: componentName,

    langModule: "leadDetail",

    components: {
        Select,
        CheckBox,
        TimelineInput
    },
    data() {
        return {
            currentType: 16,
            shareChecked: false,
            popChecked: false,
            contentLengthLimit: crmUtils.sendTextLimit,
            isDropdownOpen: false,
            showSimple: true,
            sendContent: false,
            addBTnDisabled: false,
            notAppl: this.$st("common", "notApplicable"),
            callNormPhone: getNormPhone(),
            labels: [
                {
                    id: 16,
                    name: this.$t("activities.note"),
                    ga: "LeadDetail_Activities_Add_Note",
                    addBtnText: this.$t("activities.addNoteText")
                },
                {
                    id: 25,
                    name: this.$t("activities.logCall"),
                    ga: "LeadDetail_Activities_Add_Call",
                    addBtnText: this.$t("activities.logCall")
                },
                {
                    id: 28,
                    name: this.$t("activities.logText"),
                    ga: "LeadDetail_Activities_Add_Text",
                    addBtnText: this.$t("activities.logText")
                },
                {
                    id: 7,
                    name: this.$t("activities.logEmail"),
                    ga: "LeadDetail_Activities_Add_Email",
                    addBtnText: this.$t("activities.logEmail")
                }
            ],
            callTypeList: {
                dataSource: [
                    {
                        name: this.$t("activities.talked"),
                        id: "Talked"
                    },
                    {
                        name: this.$t("activities.voiceMsg"),
                        id: "VoiceMessage"
                    },
                    {
                        name: this.$t("activities.noAnswer"),
                        id: "NoAnswer"
                    },
                    {
                        name: this.$t("activities.badNumber"),
                        id: "BadNumber"
                    },
                    {
                        name: this.$t("activities.dnc"),
                        id: "DNC"
                    }
                    // {
                    //     name: this.$t("activities.dncContact"),
                    //     id: "DNCContact"
                    // }
                ],
                value: "Talked",
                isInheritWidth: true
            }
        };
    },
    watch: {
        "$route.query"() {
            let noteType = localStore.getLocal("notetypeid");
            this.currentType = !noteType || noteType === "undefined" ? 16 : parseInt(noteType);
        },
        showSimple(val) {
            if (!val) {
                // use nextTick to prevent v-show change to v-if
                try {
                    this.$nextTick(() => {
                        this.$refs.timelineInput.$refs.inputHtml.editorPromise.then((res) => {
                            res.focus();
                        });
                    });
                } catch (error) {
                    console.log(error);
                }
            }
        }
    },
    mounted() {
        let noteType = localStore.getLocal("notetypeid");
        this.currentType = !noteType || noteType === "undefined" ? 16 : parseInt(noteType);
        if (infoData.getUserInfo().teamRoutingWhiteList) {
            this.getRelatedAgent();
        }
    },
    computed: {
        ...mapState("detail", ["phoneIconList"]),
        ...mapState("assign", ["roleList", "lenderList", "isChangeAssign"]),
        ...mapState(["leadDetail", "leadDetailIsLoad", "contactInfos"]),
        ...mapState("timeline", ["relatedAgentInfo"]),
        isLofty() {
            const { isLofty } = infoData.getUserInfo();

            return isLofty;
        },
        renderLabels({ isLofty, labels }) {
            return isLofty
                ? labels.filter((item) => {
                      return ![25, 28].includes(item.id);
                  })
                : labels;
        },
        callWrapShow() {
            return this.currentType === 25;
        },
        phoneArr() {
            let phoneList = getPhoneList(this.contactInfos);
            const _obj = {};
            let arr = [];
            phoneList.forEach((item) => {
                const _output = {
                    normPhone: item.normPhone,
                    phone: item.normPhone.number,
                    type: item.type,
                    dnc: item.dnc,
                    state: item.state
                };
                if (!_obj[item.relation]) {
                    _obj[item.relation] = true;
                    arr.push({
                        isCategory: true,
                        relation: item.relation,
                        fullName: item.fullName
                    });
                }
                arr.push(_output);
            });

            if (arr.length && !this.callNormPhone.number) {
                Object.assign(this.callNormPhone, arr[1].normPhone);
            }

            return arr;
        },
        addBtnText: function () {
            var curLabel = this.labels.find((item) => item.id === this.currentType);
            return curLabel.addBtnText;
        },
        inputConfig() {
            let categoryInfo = {};
            if (this.relatedAgentInfo.teamName && this.relatedAgentInfo.relateAgent) {
                let agentInfo = this.relatedAgentInfo.relateAgent;
                categoryInfo[this.relatedAgentInfo.teamName] = [
                    {
                        userId:
                            this.relatedAgentInfo.teamName +
                            "," +
                            this.relatedAgentInfo.relateLeadId,
                        headUrl: agentInfo.headUrl,
                        firstName: agentInfo.firstName,
                        lastName: agentInfo.lastName
                    }
                ];
            }
            return {
                lender: this.leadDetail.lender,
                privateFlag: this.leadDetail.privateFlag,
                lenderList: this.lenderList,
                roleList: this.roleList,
                leadDetailIsLoad: this.leadDetailIsLoad,
                categoryInfo: categoryInfo,
                leadId: this.leadDetail?.lead?.leadId,
                isChangeAssign: this.isChangeAssign
            };
        }
    },
    methods: {
        ...mapActions("timeline", ["updateTimeline", "getRelatedAgent"]),
        ...mapActions(["getLeadDetail", "getContactInfos"]),
        ...mapActions("smartplan", ["getSmartPlanInfoList"]),
        ...mapActions("AIAssistant", ["addAIRoles"]),
        changeMode: function (isSimple) {
            this.showSimple = isSimple;
        },
        changeType: function (type) {
            this.currentType = type;
            localStore.setLocal("notetypeid", type);
        },
        typeSelectClick(res) {
            this.changeType(res.value);
            this.changeMode(false);
        },
        getContent() {
            const { valid, tip } = this.$refs.timelineInput.checkContentLen();

            if (valid) {
                this.sendContent = true;
            } else {
                tip &&
                    utils.toast({
                        content: tip
                    });
            }

            return;
        },
        pinToTop(v) {
            track.trackGa.sendEvent("LeadDetail_Activities_Pin_Top");
            this.popChecked = v;
        },
        async addTimeline(res) {
            var curLabel = this.labels.find((item) => item.id === this.currentType);
            if (curLabel) {
                track.trackGa.sendEvent(curLabel.ga);
            }
            let content = res.content,
                tip,
                params,
                vm = this;
            // call log type ， no phone number ， Not allowed to add
            if (!this.callNormPhone.valid && this.currentType === 25) {
                tip = official.toast.logPhoneEmpty;
            }
            // note type is empty , Not allowed to add
            if (!content && this.currentType === 16) {
                tip = official.toast.contentNoEmpty;
            }
            // log Type is empty and stick to top is checked ， Not allowed to add
            if (!content && [25, 28, 7].indexOf(this.currentType) > -1 && this.popChecked) {
                tip = official.toast.logNotEmpty;
            }
            if (tip) {
                utils.toast({
                    content: tip,
                    time: 1000
                });
                this.sendContent = false;
                return;
            }
            let atUserIds = [];
            let otherIds = [];
            (res.ids || []).forEach((v) => {
                if (v && (v + "").split(",")[1]) {
                    otherIds.push((v + "").split(",")[1]);
                } else {
                    atUserIds.push(v);
                }
            });
            params = {
                timeLineContent: content,
                type: this.currentType,
                leadId: this.leadDetail.leadId,
                sticky: this.popChecked,
                atUserIds: atUserIds.join(",")
            };
            if (this.currentType === 25) {
                params.outcome = this.callTypeList.value;
                if (params.outcome == "DNC") {
                    params.dncAdd = true;
                    try {
                        let res = await http.checkDncNumberExist(
                            this.callNormPhone.number,
                            this.callNormPhone.code
                        );
                        if (!res.data[this.callNormPhone.number]?.exist) {
                            const { ok } = await popMgr.confirm({
                                title: this.$t("activities.dncLogCallTitle"),
                                desc: this.$t("activities.dncLogCall"),
                                maxWidth: "440px",
                                okText: this.$st("common", "yes1"),
                                cancelText: this.$st("common", "no1")
                            });
                            if (ok) {
                                await http.addDncNumber({
                                    phone: this.callNormPhone.number,
                                    countryCode: this.callNormPhone.code,
                                    country: this.callNormPhone.country
                                });
                            } else {
                                throw Error("cancel");
                            }
                        }
                    } catch (_err) {
                        console.log(_err);
                        this.sendContent = false;
                        return;
                    }
                }

                setAttrFromNormPhone(
                    [params, this.callNormPhone],
                    ["callNumber", "callNumberCountry", "callNumberCode"]
                );
            }
            if (otherIds.length) {
                params.relateLeadId = otherIds.join(",");
            }
            if (this.addBTnDisabled) {
                return;
            }
            this.addBTnDisabled = true;
            http.addTimeline(params)
                .then((res) => {
                    this.$refs.timelineInput.clearContent();
                    http.getTimeLineState(res.data, function () {
                        vm.addBTnDisabled = false;
                        vm.sendContent = false;
                        vm.updateTimeline();
                        vm.currentType === 25 && vm.getContactInfos();
                        vm.getLeadDetail();
                        vm.updateAIRelated();
                    });
                })
                .catch(function () {
                    vm.sendContent = false;
                    vm.addBTnDisabled = false;
                });
        },
        updateAIRelated() {
            // Log Call， and marked as Talked/Bad Number/DNC Number/DNC Contact
            const outcome = this.callTypeList?.value;
            const conditionA = this.currentType === 25 && UPDATE_AI_OUTCOME_ARR.includes(outcome);
            // Log Text
            const conditionB = this.currentType === 28;
            if (conditionA || conditionB) {
                //  need to be updated AI related modules
                this.addAIRoles();
                this.getSmartPlanInfoList();
            }
        },
        onCallPhoneChange({ data }) {
            Object.assign(this.callNormPhone, data.normPhone);
        },
        findIconCls(obj) {
            let iconCls = "",
                type = obj.type,
                iconArr =
                    !type || type == "mobile"
                        ? this.phoneIconList.mobile
                        : this.phoneIconList.other;
            let phoneIconList = this.phoneIconList.phoneList.map((v, i) => {
                return {
                    ...v,
                    iconCls: `${iconArr[i]} ${v.iconCls}`
                };
            });

            //  judge whether DNC
            if (obj.dnc) {
                let phoneIconItem = phoneIconList.find((v) => v.value === 4);
                return `icon-${phoneIconItem.iconCls}`;
            }
            for (let _item of phoneIconList) {
                if (obj[_item.key] === _item.value) {
                    iconCls = _item.iconCls;
                    break;
                }
            }
            return `icon-${iconCls}`;
        }
    }
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
}
:deep(.outcome-select-wrap) {
    height: 30px;
    line-height: 28px;
    .com-dropdown-label {
        height: 30px !important;
        line-height: 28px !important;
        color: #515666 !important;
    }
}
</style>

<style lang="less">
.detail-box {
    &.top-box {
        margin-bottom: 20px;
    }
    &.dropdown-wrap {
        &.detail-info-dropdown {
            margin-top: 6px;
            width: 250px;
            &.bottom::before,
            &.bottom::after {
                left: 95px;
            }
            li {
                & > * {
                    pointer-events: none;
                }
            }
            .btn-drop-item {
                .text {
                    max-width: unset;
                }
                &.click-item {
                    line-height: 16px;
                }
                &.desc-item {
                    font-size: 12px;
                    height: 30px;
                    color: #515666;
                    .desc {
                        max-width: 70px;
                        padding: 2px 6px;
                        color: #a0a3af;
                        border-radius: 2px;
                        background-color: #ebecf1;
                    }
                }
            }
        }
    }
    .timeline-top-simple {
        height: 50px;
        line-height: 50px;
        display: flex;
        .com-dropdown-label {
            padding-left: 20px;
            width: 136px;
            .com-dropdown-text {
                text-align: left;
            }
            .icon.right {
                right: 20px;
            }
        }
    }
    .timeline-top-select {
        width: 136px;
        font-size: 14px;
        .com-dropdown-text {
            text-align: center;
            font-size: 16px;
            color: #515666;
            font-weight: 500;
        }
    }
    .timeline-top-notetext {
        flex: 1;
        margin-right: 20px;
        input {
            width: 100%;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }
    }
    .timeline-top-complex {
        border-radius: 4px;
        background: #fff;
        display: flex;
        > ul {
            background-color: #f6f7fb;
            width: 116px;
            padding: 18px 0 20px;
            border-right: 1px solid #ebecf1;
            li {
                height: 40px;
                line-height: 40px;
                margin-top: 2px;
                padding: 0 20px;
                font-size: 16px;
                font-weight: 500;
                color: #515666;
                text-align: left;
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                &.active {
                    background-color: #fff;
                    border-top: 1px solid #ebecf1;
                    border-bottom: 1px solid #ebecf1;
                    height: 38px;
                    line-height: 38px;
                    color: var(--primary-color);
                    margin-right: -1px;
                }
            }
        }
        .content {
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            textarea {
                width: 100%;
                flex: 1;
                border-radius: 4px;
                box-shadow: inset 0 2px 4px 0 rgba(0, 10, 30, 0.1);
                border: solid 1px #c6c8d1;
                background-color: #fff;
                padding: 10px;
                box-sizing: border-box;
                color: #202437;
                font-size: 14px;
            }
        }
        .bottom-btns {
            padding-top: 16px;
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #515666;
            .pin-top {
                margin-right: 16px;
            }
        }
        .complex-btns {
            height: 36px;
            line-height: 36px;
            font-size: 0; //remove space
            flex: 1;
            text-align: right;
            .chime-btn {
                height: 36px;
                line-height: 36px;
            }
            .chime-btn + .chime-btn {
                margin-left: 10px;
            }
        }
        .call-number-list {
            display: inline-block;
            color: #787878;
            padding: 0 5px;
            margin-right: 20px;
            .com-dropdown-label {
                width: 160px;
                height: 30px;
                line-height: 30px;
            }
        }
        .add-timeline-call {
            margin-bottom: 10px;
            > li {
                display: inline-block;
                float: left;
                height: 30px;
                line-height: 30px;
            }
            .phone-span {
                display: inline-block;
                height: 30px;
                line-height: 28px;
                width: 100%;
                border: 1px solid #e1e2e6;
                border-radius: 4px;
                padding: 0 0 0 10px;
                box-sizing: border-box;
                cursor: pointer;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #515666;
                font-size: 14px;
            }
            .com-dropdownbox .com-dropdown-label {
                border-color: #e1e2e6;
            }
        }
        .call-option .list-label {
            font-size: 14px;
            vertical-align: top;
            color: #5e646c;
            margin-right: 5px;
        }
    }
}

.timeline-top-dropdown-wrap {
    width: 150px;
    position: relative;
    &:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        width: 14px;
        height: 14px;
        background-color: #fff;
    }
    .com-select-item {
        padding-left: 20px;
    }
}
</style>
